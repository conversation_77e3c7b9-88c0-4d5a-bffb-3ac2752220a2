

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">My Meetings</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('user.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item active">Meetings</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <!-- Active Meetings -->
    <?php if($activeMeetings->count() > 0): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="uil-play-circle me-2"></i>Live Meetings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php $__currentLoopData = $activeMeetings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $meeting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-success h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h6 class="card-title mb-0"><?php echo e($meeting->topic); ?></h6>
                                        <span class="badge bg-success">
                                            <i class="uil-broadcast"></i> LIVE
                                        </span>
                                    </div>
                                    
                                    <p class="text-muted small mb-3"><?php echo e(Str::limit($meeting->description, 80)); ?></p>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="uil-book-open text-muted me-2"></i>
                                            <span class="small"><?php echo e($meeting->course->title); ?></span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="uil-clock text-muted me-2"></i>
                                            <span class="small"><?php echo e($meeting->formatted_duration); ?></span>
                                        </div>
                                        <?php if($meeting->password): ?>
                                        <div class="d-flex align-items-center">
                                            <i class="uil-lock text-warning me-2"></i>
                                            <span class="small text-warning">Password Protected</span>
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <div class="d-grid gap-2">
                                        <a href="<?php echo e(route('user.meetings.join', $meeting->id)); ?>"
                                           class="btn btn-success btn-sm">
                                            <i class="uil-external-link-alt me-1"></i> Join Now
                                        </a>
                                        <a href="<?php echo e(route('user.meetings.show', $meeting->id)); ?>"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="uil-eye me-1"></i> View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Upcoming Meetings -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="uil-calendar-alt me-2"></i>Upcoming Meetings
                        </h5>
                        <div>
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshMeetings()">
                                <i class="uil-refresh me-1"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if($upcomingMeetings->count() > 0): ?>
                        <div class="row">
                            <?php $__currentLoopData = $upcomingMeetings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $meeting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card border h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h6 class="card-title mb-0"><?php echo e($meeting->topic); ?></h6>
                                            <span class="badge bg-primary">
                                                <?php echo e(ucfirst($meeting->status)); ?>

                                            </span>
                                        </div>
                                        
                                        <p class="text-muted small mb-3"><?php echo e(Str::limit($meeting->description, 80)); ?></p>
                                        
                                        <div class="mb-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="uil-book-open text-muted me-2"></i>
                                                <span class="small"><?php echo e($meeting->course->title); ?></span>
                                            </div>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="uil-calendar-alt text-muted me-2"></i>
                                                <span class="small"><?php echo e($meeting->start_time->format('M d, Y g:i A')); ?></span>
                                            </div>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="uil-clock text-muted me-2"></i>
                                                <span class="small"><?php echo e($meeting->formatted_duration); ?></span>
                                            </div>
                                            <?php if($meeting->attendance_required_for_completion): ?>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="uil-check text-info me-2"></i>
                                                <span class="small text-info">Required for completion</span>
                                            </div>
                                            <?php endif; ?>
                                            <?php if($meeting->password): ?>
                                            <div class="d-flex align-items-center">
                                                <i class="uil-lock text-warning me-2"></i>
                                                <span class="small text-warning">Password Protected</span>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Attendance Status -->
                                        <?php
                                            $userAttendance = $meeting->attendances->first();
                                        ?>
                                        <?php if($userAttendance): ?>
                                        <div class="mb-3">
                                            <div class="alert alert-info py-2 mb-0">
                                                <small>
                                                    <i class="uil-info-circle me-1"></i>
                                                    <?php if($userAttendance->attended_full_session): ?>
                                                        You have qualified attendance for this meeting
                                                    <?php else: ?>
                                                        You joined but didn't meet minimum attendance requirement
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                        <?php endif; ?>

                                        <div class="d-grid gap-2">
                                            <?php if($meeting->can_join): ?>
                                                <a href="<?php echo e(route('user.meetings.join', $meeting->id)); ?>"
                                                   class="btn btn-success btn-sm">
                                                    <i class="uil-external-link-alt me-1"></i> Join Meeting
                                                </a>
                                            <?php else: ?>
                                                <button class="btn btn-secondary btn-sm" disabled>
                                                    <i class="uil-clock me-1"></i> 
                                                    <?php if($meeting->start_time > now()): ?>
                                                        Available <?php echo e($meeting->start_time->diffForHumans()); ?>

                                                    <?php else: ?>
                                                        Meeting Ended
                                                    <?php endif; ?>
                                                </button>
                                            <?php endif; ?>
                                            <a href="<?php echo e(route('user.meetings.show', $meeting->id)); ?>"
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="uil-eye me-1"></i> View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="uil-calendar-alt display-4 text-muted mb-3"></i>
                            <h5 class="text-muted">No upcoming meetings</h5>
                            <p class="text-muted">You don't have any scheduled meetings at the moment.</p>
                            <a href="<?php echo e(route('user.courses.enrolled')); ?>" class="btn btn-primary">
                                <i class="uil-book-open me-1"></i> View My Courses
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Meeting History -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="uil-history me-2"></i>Meeting History
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Meeting</th>
                                    <th>Course</th>
                                    <th>Date</th>
                                    <th>Duration</th>
                                    <th>Attendance</th>
                                    <th>Recording</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    $allMeetings = $upcomingMeetings->merge($activeMeetings)->sortByDesc('start_time');
                                    $pastMeetings = $allMeetings->where('status', 'ended');
                                ?>
                                <?php $__empty_1 = true; $__currentLoopData = $pastMeetings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $meeting): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-1"><?php echo e($meeting->topic); ?></h6>
                                            <small class="text-muted"><?php echo e(Str::limit($meeting->description, 40)); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0 me-2">
                                                <?php if($meeting->course->thumbnail): ?>
                                                    <img src="<?php echo e(asset('storage/' . $meeting->course->thumbnail)); ?>" 
                                                         alt="<?php echo e($meeting->course->title); ?>"
                                                         class="rounded" style="width: 30px; height: 20px; object-fit: cover;">
                                                <?php else: ?>
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                         style="width: 30px; height: 20px;">
                                                        <i class="uil-book-open text-muted small"></i>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <small><?php echo e(Str::limit($meeting->course->title, 25)); ?></small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <small><?php echo e($meeting->start_time->format('M d, Y')); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info small"><?php echo e($meeting->formatted_duration); ?></span>
                                    </td>
                                    <td>
                                        <?php
                                            $userAttendance = $meeting->attendances->first();
                                        ?>
                                        <?php if($userAttendance): ?>
                                            <?php if($userAttendance->attended_full_session): ?>
                                                <span class="badge bg-success small">
                                                    <i class="uil-check me-1"></i>Qualified
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-warning small">
                                                    <i class="uil-exclamation-triangle me-1"></i>Partial
                                                </span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="badge bg-danger small">
                                                <i class="uil-times me-1"></i>Absent
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($meeting->recording_available): ?>
                                            <a href="<?php echo e(route('user.meetings.recordings', $meeting->id)); ?>"
                                               class="badge bg-success text-decoration-none small">
                                                <i class="uil-record-audio me-1"></i>Available
                                            </a>
                                        <?php else: ?>
                                            <span class="badge bg-light text-muted small">
                                                <i class="uil-times me-1"></i>N/A
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('user.meetings.show', $meeting->id)); ?>"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="uil-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="uil-history display-4 text-muted mb-3"></i>
                                        <h6 class="text-muted">No meeting history</h6>
                                        <p class="text-muted small">Your past meetings will appear here</p>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    function refreshMeetings() {
        window.location.reload();
    }

    // Auto-refresh active meetings every 30 seconds
    <?php if($activeMeetings->count() > 0): ?>
    setInterval(function() {
        // Only refresh if there are active meetings
        const activeMeetingsCount = <?php echo e($activeMeetings->count()); ?>;
        if (activeMeetingsCount > 0) {
            window.location.reload();
        }
    }, 30000);
    <?php endif; ?>
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Laravel-Apps\lernovate\resources\views/user/meetings/index.blade.php ENDPATH**/ ?>