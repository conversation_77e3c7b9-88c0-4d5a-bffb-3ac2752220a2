@extends('admin.dashboard')
@section('content')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-flex align-items-center justify-content-between">
                        <h4 class="mb-0">Complete Website Content Management</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Website CMS</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex flex-wrap gap-2">
                                <a href="{{ route('admin.homepage.seedDefaultSettings') }}" class="btn btn-info">
                                    <i class="fas fa-seedling me-2"></i>Seed Default Settings
                                </a>
                                <a href="{{ route('home') }}" target="_blank" class="btn btn-outline-primary">
                                    <i class="fas fa-eye me-2"></i>Preview Homepage
                                </a>
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addSectionModal">
                                    <i class="fas fa-plus me-2"></i>Add Custom Section
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <form action="{{ route('admin.homepage.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')

                <!-- Navigation Tabs -->
                <ul class="nav nav-tabs nav-tabs-custom nav-justified" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" data-bs-toggle="tab" href="#header-tab" role="tab">
                            <i class="fas fa-header me-2"></i>Header & Navigation
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#homepage-tab" role="tab">
                            <i class="fas fa-home me-2"></i>Homepage Content
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#footer-tab" role="tab">
                            <i class="fas fa-shoe-prints me-2"></i>Footer
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#custom-tab" role="tab">
                            <i class="fas fa-puzzle-piece me-2"></i>Custom Sections
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" data-bs-toggle="tab" href="#design-tab" role="tab">
                            <i class="fas fa-palette me-2"></i>Design & Colors
                        </a>
                    </li>
                </ul>

                <div class="tab-content p-3">
                    <!-- Header Tab -->
                    <div class="tab-pane active" id="header-tab" role="tabpanel">
                        <div class="card">
                            <div class="card-header"><h5><i class="fas fa-header me-2"></i>Header & Navigation Settings</h5></div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Site Name</label>
                                        <input type="text" class="form-control" name="settings[site_name]" value="{{ \App\Models\SiteSetting::get('site_name', 'Diagnostic Intelligence') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Logo</label>
                                        <input type="file" class="form-control" name="files[logo]" accept="image/*" onchange="previewImage(this, 'logo_preview')">
                                        <div class="mt-2">
                                            <img id="logo_preview" src="{{ \App\Models\SiteSetting::getImageUrl('logo', 'assets/images/logo.png') }}" alt="Logo" class="img-thumbnail" style="max-width: 150px; max-height: 80px;">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Contact Email</label>
                                        <input type="email" class="form-control" name="settings[contact_email]" value="{{ \App\Models\SiteSetting::get('contact_email', '<EMAIL>') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Contact Phone</label>
                                        <input type="tel" class="form-control" name="settings[contact_phone]" value="{{ \App\Models\SiteSetting::get('contact_phone', '+254 700 000 000') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Homepage Tab -->
                    <div class="tab-pane" id="homepage-tab" role="tabpanel">
                        <!-- Hero Section -->
                        <div class="card mb-4">
                            <div class="card-header"><h5><i class="fas fa-star me-2"></i>Hero Section</h5></div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Hero Title</label>
                                        <input type="text" class="form-control" name="settings[hero_title]" value="{{ \App\Models\SiteSetting::get('hero_title', 'Master Professional Skills with') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Hero Highlight Text</label>
                                        <input type="text" class="form-control" name="settings[hero_highlight_text]" value="{{ \App\Models\SiteSetting::get('hero_highlight_text', 'CPD Programs') }}">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">Hero Description</label>
                                        <textarea class="form-control" rows="3" name="settings[hero_description]">{{ \App\Models\SiteSetting::get('hero_description', 'Transform your career with industry-recognized CPD courses.') }}</textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Hero Image</label>
                                        <input type="file" class="form-control" name="files[hero_image]" accept="image/*" onchange="previewImage(this, 'hero_image_preview')">
                                        <div class="mt-2">
                                            <img id="hero_image_preview" src="{{ \App\Models\SiteSetting::getImageUrl('hero_image', 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp') }}" alt="Hero Image" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Hero Video URL (YouTube)</label>
                                        <input type="url" class="form-control" name="settings[hero_video_url]" value="{{ \App\Models\SiteSetting::get('hero_video_url', '') }}" placeholder="https://www.youtube.com/watch?v=...">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Button Text</label>
                                        <input type="text" class="form-control" name="settings[hero_button_text]" value="{{ \App\Models\SiteSetting::get('hero_button_text', 'Explore Courses') }}">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Feature 1</label>
                                        <input type="text" class="form-control" name="settings[hero_feature1]" value="{{ \App\Models\SiteSetting::get('hero_feature1', 'Accredited Certificates') }}">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Feature 2</label>
                                        <input type="text" class="form-control" name="settings[hero_feature2]" value="{{ \App\Models\SiteSetting::get('hero_feature2', 'Flexible Learning') }}">
                                    </div>
                                </div>
                                
                                <!-- Hero Authors Section -->
                                <h6 class="mt-4 mb-3">Hero Authors/Testimonials</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Author 1 Image</label>
                                        <input type="file" class="form-control" name="files[hero_author1_image]" accept="image/*" onchange="previewImage(this, 'author1_image_preview')">
                                        <div class="mt-2">
                                            <img id="author1_image_preview" src="{{ \App\Models\SiteSetting::getImageUrl('hero_author1_image', 'frontend/img/banner/banner_author01.png') }}" alt="Author 1" class="img-thumbnail rounded-circle" style="width: 80px; height: 80px; object-fit: cover;">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Author 2 Image</label>
                                        <input type="file" class="form-control" name="files[hero_author2_image]" accept="image/*" onchange="previewImage(this, 'author2_image_preview')">
                                        <div class="mt-2">
                                            <img id="author2_image_preview" src="{{ \App\Models\SiteSetting::getImageUrl('hero_author2_image', 'frontend/img/banner/banner_author02.png') }}" alt="Author 2" class="img-thumbnail rounded-circle" style="width: 80px; height: 80px; object-fit: cover;">
                                        </div>
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Author 1 Name</label>
                                        <input type="text" class="form-control" name="settings[hero_author1_name]" value="{{ \App\Models\SiteSetting::get('hero_author1_name', 'Robert Fox') }}">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Author 1 Title</label>
                                        <input type="text" class="form-control" name="settings[hero_author1_title]" value="{{ \App\Models\SiteSetting::get('hero_author1_title', 'Diagnostic Expert') }}">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Author 2 Name</label>
                                        <input type="text" class="form-control" name="settings[hero_author2_name]" value="{{ \App\Models\SiteSetting::get('hero_author2_name', 'Michel Jones') }}">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Author 2 Title</label>
                                        <input type="text" class="form-control" name="settings[hero_author2_title]" value="{{ \App\Models\SiteSetting::get('hero_author2_title', 'Medical Specialist') }}">
                                    </div>
                                </div>
                                
                                <!-- Hero Background -->
                                <h6 class="mt-4 mb-3">Hero Background</h6>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Hero Background Image</label>
                                        <input type="file" class="form-control" name="files[hero_bg_image]" accept="image/*" onchange="previewImage(this, 'bg_image_preview')">
                                        <div class="mt-2">
                                            <img id="bg_image_preview" src="{{ \App\Models\SiteSetting::getImageUrl('hero_bg_image', 'frontend/img/banner/banner_bg.png') }}" alt="Background" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Hero Background Color</label>
                                        <input type="color" class="form-control form-control-color" name="settings[hero_bg_color]" value="{{ \App\Models\SiteSetting::get('hero_bg_color', '#f8f9fa') }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- About Section -->
                        <div class="card mb-4">
                            <div class="card-header"><h5><i class="fas fa-info-circle me-2"></i>About Section</h5></div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">About Subtitle</label>
                                        <input type="text" class="form-control" name="settings[about_subtitle]" value="{{ \App\Models\SiteSetting::get('about_subtitle', 'Get To Know About Us') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">About Title</label>
                                        <input type="text" class="form-control" name="settings[about_title]" value="{{ \App\Models\SiteSetting::get('about_title', 'Empowering Minds,') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Highlight Text</label>
                                        <input type="text" class="form-control" name="settings[about_highlight_text]" value="{{ \App\Models\SiteSetting::get('about_highlight_text', 'Shaping') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Title End</label>
                                        <input type="text" class="form-control" name="settings[about_title_end]" value="{{ \App\Models\SiteSetting::get('about_title_end', 'Futures') }}">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">About Description</label>
                                        <textarea class="form-control" rows="3" name="settings[about_description]">{{ \App\Models\SiteSetting::get('about_description', 'We are committed to providing high-quality professional development.') }}</textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">About Image</label>
                                        <input type="file" class="form-control" name="files[about_image]" accept="image/*" onchange="previewImage(this, 'about_image_preview')">
                                        <div class="mt-2">
                                            <img id="about_image_preview" src="{{ \App\Models\SiteSetting::getImageUrl('about_image', 'uploads/custom-images/wsus-img-2024-06-04-11-18-08-2099.webp') }}" alt="About Image" class="img-thumbnail" style="max-width: 200px; max-height: 150px;">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Button Text</label>
                                        <input type="text" class="form-control" name="settings[about_button_text]" value="{{ \App\Models\SiteSetting::get('about_button_text', 'Start Free Trial') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Feature 1</label>
                                        <input type="text" class="form-control" name="settings[about_feature1]" value="{{ \App\Models\SiteSetting::get('about_feature1', 'Industry-Leading Expert Instructors') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Feature 2</label>
                                        <input type="text" class="form-control" name="settings[about_feature2]" value="{{ \App\Models\SiteSetting::get('about_feature2', 'Learn Anywhere, Anytime') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Feature 3</label>
                                        <input type="text" class="form-control" name="settings[about_feature3]" value="{{ \App\Models\SiteSetting::get('about_feature3', 'Personalized Learning Paths') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Feature 4</label>
                                        <input type="text" class="form-control" name="settings[about_feature4]" value="{{ \App\Models\SiteSetting::get('about_feature4', 'Accredited CPD Certificates') }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- CTA Section -->
                        <div class="card mb-4">
                            <div class="card-header"><h5><i class="fas fa-bullhorn me-2"></i>Call to Action</h5></div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">CTA Title</label>
                                        <input type="text" class="form-control" name="settings[cta_title]" value="{{ \App\Models\SiteSetting::get('cta_title', 'Ready to Start Your Learning Journey?') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Button 1 Text</label>
                                        <input type="text" class="form-control" name="settings[cta_button1_text]" value="{{ \App\Models\SiteSetting::get('cta_button1_text', 'Browse Courses') }}">
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">CTA Description</label>
                                        <textarea class="form-control" rows="2" name="settings[cta_description]">{{ \App\Models\SiteSetting::get('cta_description', 'Join thousands of professionals who have advanced their careers') }}</textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Button 2 Text</label>
                                        <input type="text" class="form-control" name="settings[cta_button2_text]" value="{{ \App\Models\SiteSetting::get('cta_button2_text', 'Learn More') }}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Statistics -->
                        <div class="card mb-4">
                            <div class="card-header"><h5><i class="fas fa-chart-bar me-2"></i>Statistics Section</h5></div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Students Count</label>
                                        <input type="text" class="form-control" name="settings[stats_students]" value="{{ \App\Models\SiteSetting::get('stats_students', '25K') }}">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">Students Text</label>
                                        <input type="text" class="form-control" name="settings[stats_students_text]" value="{{ \App\Models\SiteSetting::get('stats_students_text', 'Active Students') }}">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">CPD Programs</label>
                                        <input type="text" class="form-control" name="settings[stats_cpd_programs]" value="{{ \App\Models\SiteSetting::get('stats_cpd_programs', '10') }}">
                                    </div>
                                    <div class="col-md-3 mb-3">
                                        <label class="form-label">CPD Text</label>
                                        <input type="text" class="form-control" name="settings[stats_cpd_text]" value="{{ \App\Models\SiteSetting::get('stats_cpd_text', 'CPD Programs') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Satisfaction Rate</label>
                                        <input type="text" class="form-control" name="settings[stats_satisfaction]" value="{{ \App\Models\SiteSetting::get('stats_satisfaction', '99%') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Satisfaction Text</label>
                                        <input type="text" class="form-control" name="settings[stats_satisfaction_text]" value="{{ \App\Models\SiteSetting::get('stats_satisfaction_text', 'Satisfaction Rate') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Footer Tab -->
                    <div class="tab-pane" id="footer-tab" role="tabpanel">
                        <div class="card">
                            <div class="card-header"><h5><i class="fas fa-shoe-prints me-2"></i>Footer Settings</h5></div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label class="form-label">Footer Description</label>
                                        <textarea class="form-control" rows="3" name="settings[footer_text]">{{ \App\Models\SiteSetting::get('footer_text', 'Empowering healthcare professionals with cutting-edge diagnostic education.') }}</textarea>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Copyright Text</label>
                                        <input type="text" class="form-control" name="settings[copyright_text]" value="{{ \App\Models\SiteSetting::get('copyright_text', '© 2025 Diagnostic Intelligence. All rights reserved.') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Footer Logo</label>
                                        <input type="file" class="form-control" name="files[footer_logo]" accept="image/*" onchange="previewImage(this, 'footer_logo_preview')">
                                        <div class="mt-2">
                                            <img id="footer_logo_preview" src="{{ \App\Models\SiteSetting::getImageUrl('footer_logo', 'assets/images/logo.png') }}" alt="Footer Logo" class="img-thumbnail" style="max-width: 150px; max-height: 80px;">
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">Facebook URL</label>
                                        <input type="url" class="form-control" name="settings[social_facebook]" value="{{ \App\Models\SiteSetting::get('social_facebook', 'https://www.facebook.com/') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">LinkedIn URL</label>
                                        <input type="url" class="form-control" name="settings[social_linkedin]" value="{{ \App\Models\SiteSetting::get('social_linkedin', 'https://www.linkedin.com/') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">YouTube URL</label>
                                        <input type="url" class="form-control" name="settings[social_youtube]" value="{{ \App\Models\SiteSetting::get('social_youtube', 'https://www.youtube.com/') }}">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">WhatsApp URL</label>
                                        <input type="url" class="form-control" name="settings[social_whatsapp]" value="{{ \App\Models\SiteSetting::get('social_whatsapp', 'https://web.whatsapp.com/') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Custom Sections Tab -->
                    <div class="tab-pane" id="custom-tab" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-puzzle-piece me-2"></i>Custom Sections</h5>
                                <small class="text-muted">Add custom sections like testimonials, features, videos, etc.</small>
                            </div>
                            <div class="card-body">
                                @if($customSections->count() > 0)
                                    @foreach($customSections as $section)
                                        <div class="border p-3 mb-3 rounded">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                @php
                                                    $sectionData = is_array($section->value) ? $section->value : json_decode($section->value, true);
                                                @endphp
                                                <h6>{{ $sectionData['name'] ?? $section->key }} <small class="text-muted">({{ ucfirst($sectionData['type'] ?? 'text') }})</small></h6>
                                                <form action="{{ route('admin.homepage.deleteCustomSection', $section->key) }}" method="POST" style="display: inline;">
                                                    @csrf
                                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Delete this section?')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                            @php
                                                $sectionData = is_array($section->value) ? $section->value : json_decode($section->value, true);
                                            @endphp
                                            <textarea class="form-control" name="settings[{{ $section->key }}_content]" rows="3" placeholder="Enter content for this section">{{ $sectionData['content'] ?? '' }}</textarea>
                                            <input type="hidden" name="settings[{{ $section->key }}_enabled]" value="1">
                                        </div>
                                    @endforeach
                                @else
                                    <p class="text-muted">No custom sections added yet. Click "Add Custom Section" to create one.</p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Design Tab -->
                    <div class="tab-pane" id="design-tab" role="tabpanel">
                        <div class="card">
                            <div class="card-header"><h5><i class="fas fa-palette me-2"></i>Design & Colors</h5></div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Primary Color</label>
                                        <input type="color" class="form-control form-control-color" name="settings[primary_color]" value="{{ \App\Models\SiteSetting::get('primary_color', '#5751e1') }}">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Secondary Color</label>
                                        <input type="color" class="form-control form-control-color" name="settings[secondary_color]" value="{{ \App\Models\SiteSetting::get('secondary_color', '#3fdacf') }}">
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Accent Color</label>
                                        <input type="color" class="form-control form-control-color" name="settings[accent_color]" value="{{ \App\Models\SiteSetting::get('accent_color', '#ffc224') }}">
                                    </div>
                                </div>
                                
                                <h6 class="mt-4 mb-3">Typography</h6>
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Primary Font Family</label>
                                        <select class="form-control" name="settings[primary_font]">
                                            <option value="Inter" {{ \App\Models\SiteSetting::get('primary_font', 'Inter') == 'Inter' ? 'selected' : '' }}>Inter</option>
                                            <option value="Roboto" {{ \App\Models\SiteSetting::get('primary_font', 'Inter') == 'Roboto' ? 'selected' : '' }}>Roboto</option>
                                            <option value="Open Sans" {{ \App\Models\SiteSetting::get('primary_font', 'Inter') == 'Open Sans' ? 'selected' : '' }}>Open Sans</option>
                                            <option value="Lato" {{ \App\Models\SiteSetting::get('primary_font', 'Inter') == 'Lato' ? 'selected' : '' }}>Lato</option>
                                            <option value="Poppins" {{ \App\Models\SiteSetting::get('primary_font', 'Inter') == 'Poppins' ? 'selected' : '' }}>Poppins</option>
                                            <option value="Montserrat" {{ \App\Models\SiteSetting::get('primary_font', 'Inter') == 'Montserrat' ? 'selected' : '' }}>Montserrat</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Heading Font Size (Hero)</label>
                                        <input type="number" class="form-control" name="settings[hero_title_size]" value="{{ \App\Models\SiteSetting::get('hero_title_size', '48') }}" min="20" max="80">
                                        <small class="text-muted">Size in pixels</small>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <label class="form-label">Hero Text Color</label>
                                        <input type="color" class="form-control form-control-color" name="settings[hero_text_color]" value="{{ \App\Models\SiteSetting::get('hero_text_color', '#1a1a1a') }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mb-4">
                    <button type="submit" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-save me-2"></i>Update All Content
                    </button>
                    <a href="{{ route('home') }}" target="_blank" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-eye me-2"></i>Preview Changes
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Custom Section Modal -->
<div class="modal fade" id="addSectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{{ route('admin.homepage.addCustomSection') }}" method="POST">
                @csrf
                <div class="modal-header">
                    <h5 class="modal-title">Add Custom Section</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Section Name</label>
                        <input type="text" class="form-control" name="section_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Section Type</label>
                        <select class="form-control" name="section_type" required>
                            <option value="text">Text Content</option>
                            <option value="image">Image Gallery</option>
                            <option value="video">Video Section</option>
                            <option value="testimonial">Testimonials</option>
                            <option value="features">Features List</option>
                            <option value="stats">Statistics</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Section</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function previewImage(input, previewId) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById(previewId).src = e.target.result;
        }
        reader.readAsDataURL(input.files[0]);
    }
}

// Color picker live preview
document.addEventListener('DOMContentLoaded', function() {
    const colorInputs = document.querySelectorAll('input[type="color"]');
    colorInputs.forEach(input => {
        input.addEventListener('change', function() {
            // You can add live color preview functionality here
            console.log('Color changed:', this.name, this.value);
        });
    });
    
    // Font size live preview
    const fontSizeInput = document.querySelector('input[name="settings[hero_title_size]"]');
    if (fontSizeInput) {
        fontSizeInput.addEventListener('input', function() {
            console.log('Font size changed:', this.value + 'px');
        });
    }
});
</script>
@endsection