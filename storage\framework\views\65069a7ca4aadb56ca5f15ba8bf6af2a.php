

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">Meeting Details</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('user.dashboard')); ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('user.meetings.index')); ?>">Meetings</a></li>
                        <li class="breadcrumb-item active"><?php echo e($meeting->topic); ?></li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <!-- Meeting Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <!-- Meeting Header -->
                    <div class="d-flex justify-content-between align-items-start mb-4">
                        <div>
                            <h4 class="card-title mb-2"><?php echo e($meeting->topic); ?></h4>
                            <p class="card-title-desc text-muted"><?php echo e($meeting->description); ?></p>
                            
                            <!-- Status Badge -->
                            <div class="mb-3">
                                <?php switch($meeting->status):
                                    case ('scheduled'): ?>
                                        <span class="badge bg-primary fs-6">
                                            <i class="uil-calendar-alt me-1"></i>Scheduled
                                        </span>
                                        <?php break; ?>
                                    <?php case ('started'): ?>
                                        <span class="badge bg-success fs-6">
                                            <i class="uil-play-circle me-1"></i>Live Now
                                        </span>
                                        <?php break; ?>
                                    <?php case ('ended'): ?>
                                        <span class="badge bg-secondary fs-6">
                                            <i class="uil-stop-circle me-1"></i>Ended
                                        </span>
                                        <?php break; ?>
                                    <?php case ('cancelled'): ?>
                                        <span class="badge bg-danger fs-6">
                                            <i class="uil-times-circle me-1"></i>Cancelled
                                        </span>
                                        <?php break; ?>
                                <?php endswitch; ?>

                                <?php if($meeting->attendance_required_for_completion): ?>
                                    <span class="badge bg-info fs-6 ms-2">
                                        <i class="uil-check me-1"></i>Required for Completion
                                    </span>
                                <?php endif; ?>

                                <?php if($meeting->password): ?>
                                    <span class="badge bg-warning fs-6 ms-2">
                                        <i class="uil-lock me-1"></i>Password Protected
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="d-flex flex-column gap-2">
                            <?php if($meeting->can_join): ?>
                                <a href="<?php echo e(route('user.meetings.join', $meeting->id)); ?>"
                                   class="btn btn-success">
                                    <i class="uil-external-link-alt me-1"></i> Join Meeting
                                </a>
                            <?php elseif($meeting->status === 'scheduled'): ?>
                                <button class="btn btn-secondary" disabled>
                                    <i class="uil-clock me-1"></i> 
                                    Available <?php echo e($meeting->start_time->diffForHumans()); ?>

                                </button>
                            <?php endif; ?>

                            <?php if($meeting->recording_available && $meeting->recording_url): ?>
                                <a href="<?php echo e(route('user.meetings.recordings', $meeting->id)); ?>"
                                   class="btn btn-outline-primary">
                                    <i class="uil-record-audio me-1"></i> View Recording
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Meeting Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <tbody>
                                        <tr>
                                            <td class="fw-bold text-muted">Meeting ID:</td>
                                            <td>
                                                <code><?php echo e($meeting->zoom_meeting_id); ?></code>
                                                <button class="btn btn-link btn-sm p-0 ms-2" 
                                                        onclick="copyToClipboard('<?php echo e($meeting->zoom_meeting_id); ?>')"
                                                        title="Copy Meeting ID">
                                                    <i class="uil-copy"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Date & Time:</td>
                                            <td><?php echo e($meeting->formatted_start_time); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Duration:</td>
                                            <td><?php echo e($meeting->formatted_duration); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Timezone:</td>
                                            <td><?php echo e($meeting->timezone); ?></td>
                                        </tr>
                                        <?php if($meeting->password): ?>
                                        <tr>
                                            <td class="fw-bold text-muted">Password:</td>
                                            <td>
                                                <code id="meetingPassword" style="display: none;"><?php echo e($meeting->password); ?></code>
                                                <button class="btn btn-outline-warning btn-sm" 
                                                        onclick="togglePassword()"
                                                        id="passwordToggle">
                                                    <i class="uil-eye me-1"></i> Show Password
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <tbody>
                                        <tr>
                                            <td class="fw-bold text-muted">Min. Attendance:</td>
                                            <td><?php echo e($meeting->minimum_attendance_minutes); ?> minutes</td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Your Status:</td>
                                            <td>
                                                <?php if($attendance): ?>
                                                    <?php if($attendance->attended_full_session): ?>
                                                        <span class="badge bg-success">
                                                            <i class="uil-check me-1"></i>Qualified
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">
                                                            <i class="uil-exclamation-triangle me-1"></i>Partial Attendance
                                                        </span>
                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <span class="badge bg-light text-muted">
                                                        <i class="uil-minus me-1"></i>Not Attended
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php if($attendance): ?>
                                        <tr>
                                            <td class="fw-bold text-muted">Join Time:</td>
                                            <td><?php echo e($attendance->joined_at ? $attendance->joined_at->format('M d, Y g:i A') : 'N/A'); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="fw-bold text-muted">Duration:</td>
                                            <td><?php echo e($attendance->duration_minutes); ?> minutes</td>
                                        </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Course Information -->
                    <div class="border-top pt-4 mt-4">
                        <h5 class="mb-3">Associated Course</h5>
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-3">
                                <?php if($meeting->course->thumbnail): ?>
                                    <img src="<?php echo e(asset('storage/' . $meeting->course->thumbnail)); ?>" 
                                         alt="<?php echo e($meeting->course->title); ?>"
                                         class="rounded" style="width: 80px; height: 60px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                         style="width: 80px; height: 60px;">
                                        <i class="uil-book-open text-muted"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="mb-1"><?php echo e($meeting->course->title); ?></h6>
                                <p class="text-muted mb-2"><?php echo e($meeting->course->short_description); ?></p>
                                <div>
                                    <span class="badge bg-primary"><?php echo e($meeting->course->category->name ?? 'Uncategorized'); ?></span>
                                    <?php if($meeting->course->is_featured): ?>
                                        <span class="badge bg-warning"><i class="uil-star"></i> Featured</span>
                                    <?php endif; ?>
                                    <?php if($meeting->course->is_free): ?>
                                        <span class="badge bg-success">Free</span>
                                    <?php else: ?>
                                        <span class="badge bg-info">KES <?php echo e(number_format($meeting->course->price, 0)); ?></span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div>
                                <a href="<?php echo e(route('course.show', $meeting->course->slug)); ?>" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="uil-external-link-alt me-1"></i> View Course
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Meeting Instructions -->
                    <?php if($meeting->course->meeting_instructions): ?>
                    <div class="border-top pt-4 mt-4">
                        <h5 class="mb-3">Meeting Instructions</h5>
                        <div class="alert alert-info">
                            <i class="uil-info-circle me-2"></i>
                            <?php echo e($meeting->course->meeting_instructions); ?>

                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Quick Actions</h5>
                    
                    <div class="d-grid gap-2">
                        <?php if($meeting->can_join): ?>
                            <a href="<?php echo e(route('user.meetings.join', $meeting->id)); ?>"
                               class="btn btn-success">
                                <i class="uil-external-link-alt me-2"></i>Join Meeting
                            </a>
                        <?php elseif($meeting->status === 'scheduled'): ?>
                            <button class="btn btn-secondary" disabled>
                                <i class="uil-clock me-2"></i>
                                Available <?php echo e($meeting->start_time->diffForHumans()); ?>

                            </button>
                        <?php endif; ?>

                        <?php if($meeting->recording_available && $meeting->recording_url): ?>
                            <a href="<?php echo e(route('user.meetings.recordings', $meeting->id)); ?>"
                               class="btn btn-outline-primary">
                                <i class="uil-record-audio me-2"></i>View Recording
                            </a>
                        <?php endif; ?>

                        <a href="<?php echo e(route('course.show', $meeting->course->slug)); ?>" 
                           class="btn btn-outline-secondary">
                            <i class="uil-book-open me-2"></i>View Course
                        </a>

                        <a href="<?php echo e(route('user.meetings.index')); ?>"
                           class="btn btn-outline-info">
                            <i class="uil-arrow-left me-2"></i>Back to Meetings
                        </a>
                    </div>
                </div>
            </div>

            <!-- Meeting Countdown -->
            <?php if($meeting->status === 'scheduled' && $meeting->start_time > now()): ?>
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Meeting Starts In</h5>
                    <div id="countdown" class="display-6 text-primary mb-3"></div>
                    <p class="text-muted small">You can join 15 minutes before the scheduled time</p>
                </div>
            </div>
            <?php endif; ?>

            <!-- Meeting Tips -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Meeting Tips</h5>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="uil-check text-success me-2"></i>
                            <small>Test your audio and video before joining</small>
                        </li>
                        <li class="mb-2">
                            <i class="uil-check text-success me-2"></i>
                            <small>Join from a quiet environment</small>
                        </li>
                        <li class="mb-2">
                            <i class="uil-check text-success me-2"></i>
                            <small>Have a stable internet connection</small>
                        </li>
                        <?php if($meeting->attendance_required_for_completion): ?>
                        <li class="mb-2">
                            <i class="uil-exclamation-triangle text-warning me-2"></i>
                            <small>Stay for at least <?php echo e($meeting->minimum_attendance_minutes); ?> minutes to qualify</small>
                        </li>
                        <?php endif; ?>
                        <?php if($meeting->password): ?>
                        <li class="mb-0">
                            <i class="uil-lock text-info me-2"></i>
                            <small>You'll need the meeting password to join</small>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- Technical Requirements -->
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Technical Requirements</h5>
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="uil-desktop text-primary me-2"></i>
                            <small>Computer, tablet, or smartphone</small>
                        </li>
                        <li class="mb-2">
                            <i class="uil-wifi text-primary me-2"></i>
                            <small>Stable internet connection</small>
                        </li>
                        <li class="mb-2">
                            <i class="uil-microphone text-primary me-2"></i>
                            <small>Microphone (recommended)</small>
                        </li>
                        <li class="mb-0">
                            <i class="uil-video text-primary me-2"></i>
                            <small>Camera (optional)</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(function() {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="uil-check me-2"></i>Copied to clipboard!
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            document.body.appendChild(toast);
            
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            // Remove toast after it's hidden
            toast.addEventListener('hidden.bs.toast', function() {
                document.body.removeChild(toast);
            });
        }).catch(function(err) {
            console.error('Could not copy text: ', err);
            alert('Failed to copy to clipboard');
        });
    }

    function togglePassword() {
        const passwordElement = document.getElementById('meetingPassword');
        const toggleButton = document.getElementById('passwordToggle');
        
        if (passwordElement.style.display === 'none') {
            passwordElement.style.display = 'inline';
            toggleButton.innerHTML = '<i class="uil-eye-slash me-1"></i> Hide Password';
        } else {
            passwordElement.style.display = 'none';
            toggleButton.innerHTML = '<i class="uil-eye me-1"></i> Show Password';
        }
    }

    // Countdown timer for scheduled meetings
    <?php if($meeting->status === 'scheduled' && $meeting->start_time > now()): ?>
    function updateCountdown() {
        const meetingTime = new Date('<?php echo e($meeting->start_time->toISOString()); ?>').getTime();
        const now = new Date().getTime();
        const distance = meetingTime - now;

        if (distance > 0) {
            const days = Math.floor(distance / (1000 * 60 * 60 * 24));
            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            let countdownText = '';
            if (days > 0) {
                countdownText = `${days}d ${hours}h ${minutes}m`;
            } else if (hours > 0) {
                countdownText = `${hours}h ${minutes}m ${seconds}s`;
            } else {
                countdownText = `${minutes}m ${seconds}s`;
            }

            document.getElementById('countdown').innerHTML = countdownText;
        } else {
            document.getElementById('countdown').innerHTML = 'Meeting Started!';
            // Refresh page when meeting starts
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        }
    }

    // Update countdown every second
    updateCountdown();
    setInterval(updateCountdown, 1000);
    <?php endif; ?>

    // Auto-refresh for active meetings
    <?php if($meeting->status === 'started'): ?>
    setInterval(function() {
        window.location.reload();
    }, 30000); // Refresh every 30 seconds
    <?php endif; ?>
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Laravel-Apps\lernovate\resources\views/user/meetings/show.blade.php ENDPATH**/ ?>