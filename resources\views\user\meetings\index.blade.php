@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-flex align-items-center justify-content-between">
                <h4 class="mb-0">My Meetings</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{{ route('user.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item active">Meetings</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Active Meetings -->
    @if($activeMeetings->count() > 0)
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="uil-play-circle me-2"></i>Live Meetings
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($activeMeetings as $meeting)
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-success h-100">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-3">
                                        <h6 class="card-title mb-0">{{ $meeting->topic }}</h6>
                                        <span class="badge bg-success">
                                            <i class="uil-broadcast"></i> LIVE
                                        </span>
                                    </div>
                                    
                                    <p class="text-muted small mb-3">{{ Str::limit($meeting->description, 80) }}</p>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="uil-book-open text-muted me-2"></i>
                                            <span class="small">{{ $meeting->course->title }}</span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <i class="uil-clock text-muted me-2"></i>
                                            <span class="small">{{ $meeting->formatted_duration }}</span>
                                        </div>
                                        @if($meeting->password)
                                        <div class="d-flex align-items-center">
                                            <i class="uil-lock text-warning me-2"></i>
                                            <span class="small text-warning">Password Protected</span>
                                        </div>
                                        @endif
                                    </div>

                                    <div class="d-grid gap-2">
                                        <a href="{{ route('user.meetings.join', $meeting->id) }}"
                                           class="btn btn-success btn-sm">
                                            <i class="uil-external-link-alt me-1"></i> Join Now
                                        </a>
                                        <a href="{{ route('user.meetings.show', $meeting->id) }}"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="uil-eye me-1"></i> View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Upcoming Meetings -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="uil-calendar-alt me-2"></i>Upcoming Meetings
                        </h5>
                        <div>
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshMeetings()">
                                <i class="uil-refresh me-1"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if($upcomingMeetings->count() > 0)
                        <div class="row">
                            @foreach($upcomingMeetings as $meeting)
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card border h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h6 class="card-title mb-0">{{ $meeting->topic }}</h6>
                                            <span class="badge bg-primary">
                                                {{ ucfirst($meeting->status) }}
                                            </span>
                                        </div>
                                        
                                        <p class="text-muted small mb-3">{{ Str::limit($meeting->description, 80) }}</p>
                                        
                                        <div class="mb-3">
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="uil-book-open text-muted me-2"></i>
                                                <span class="small">{{ $meeting->course->title }}</span>
                                            </div>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="uil-calendar-alt text-muted me-2"></i>
                                                <span class="small">{{ $meeting->start_time->format('M d, Y g:i A') }}</span>
                                            </div>
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="uil-clock text-muted me-2"></i>
                                                <span class="small">{{ $meeting->formatted_duration }}</span>
                                            </div>
                                            @if($meeting->attendance_required_for_completion)
                                            <div class="d-flex align-items-center mb-2">
                                                <i class="uil-check text-info me-2"></i>
                                                <span class="small text-info">Required for completion</span>
                                            </div>
                                            @endif
                                            @if($meeting->password)
                                            <div class="d-flex align-items-center">
                                                <i class="uil-lock text-warning me-2"></i>
                                                <span class="small text-warning">Password Protected</span>
                                            </div>
                                            @endif
                                        </div>

                                        <!-- Attendance Status -->
                                        @php
                                            $userAttendance = $meeting->attendances->first();
                                        @endphp
                                        @if($userAttendance)
                                        <div class="mb-3">
                                            <div class="alert alert-info py-2 mb-0">
                                                <small>
                                                    <i class="uil-info-circle me-1"></i>
                                                    @if($userAttendance->attended_full_session)
                                                        You have qualified attendance for this meeting
                                                    @else
                                                        You joined but didn't meet minimum attendance requirement
                                                    @endif
                                                </small>
                                            </div>
                                        </div>
                                        @endif

                                        <div class="d-grid gap-2">
                                            @if($meeting->can_join)
                                                <a href="{{ route('user.meetings.join', $meeting->id) }}"
                                                   class="btn btn-success btn-sm">
                                                    <i class="uil-external-link-alt me-1"></i> Join Meeting
                                                </a>
                                            @else
                                                <button class="btn btn-secondary btn-sm" disabled>
                                                    <i class="uil-clock me-1"></i> 
                                                    @if($meeting->start_time > now())
                                                        Available {{ $meeting->start_time->diffForHumans() }}
                                                    @else
                                                        Meeting Ended
                                                    @endif
                                                </button>
                                            @endif
                                            <a href="{{ route('user.meetings.show', $meeting->id) }}"
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="uil-eye me-1"></i> View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="uil-calendar-alt display-4 text-muted mb-3"></i>
                            <h5 class="text-muted">No upcoming meetings</h5>
                            <p class="text-muted">You don't have any scheduled meetings at the moment.</p>
                            <a href="{{ route('user.courses.enrolled') }}" class="btn btn-primary">
                                <i class="uil-book-open me-1"></i> View My Courses
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Meeting History -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="uil-history me-2"></i>Meeting History
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Meeting</th>
                                    <th>Course</th>
                                    <th>Date</th>
                                    <th>Duration</th>
                                    <th>Attendance</th>
                                    <th>Recording</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $allMeetings = $upcomingMeetings->merge($activeMeetings)->sortByDesc('start_time');
                                    $pastMeetings = $allMeetings->where('status', 'ended');
                                @endphp
                                @forelse($pastMeetings as $meeting)
                                <tr>
                                    <td>
                                        <div>
                                            <h6 class="mb-1">{{ $meeting->topic }}</h6>
                                            <small class="text-muted">{{ Str::limit($meeting->description, 40) }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0 me-2">
                                                @if($meeting->course->thumbnail)
                                                    <img src="{{ asset('storage/' . $meeting->course->thumbnail) }}" 
                                                         alt="{{ $meeting->course->title }}"
                                                         class="rounded" style="width: 30px; height: 20px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                         style="width: 30px; height: 20px;">
                                                        <i class="uil-book-open text-muted small"></i>
                                                    </div>
                                                @endif
                                            </div>
                                            <div>
                                                <small>{{ Str::limit($meeting->course->title, 25) }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <small>{{ $meeting->start_time->format('M d, Y') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info small">{{ $meeting->formatted_duration }}</span>
                                    </td>
                                    <td>
                                        @php
                                            $userAttendance = $meeting->attendances->first();
                                        @endphp
                                        @if($userAttendance)
                                            @if($userAttendance->attended_full_session)
                                                <span class="badge bg-success small">
                                                    <i class="uil-check me-1"></i>Qualified
                                                </span>
                                            @else
                                                <span class="badge bg-warning small">
                                                    <i class="uil-exclamation-triangle me-1"></i>Partial
                                                </span>
                                            @endif
                                        @else
                                            <span class="badge bg-danger small">
                                                <i class="uil-times me-1"></i>Absent
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($meeting->recording_available)
                                            <a href="{{ route('user.meetings.recordings', $meeting->id) }}"
                                               class="badge bg-success text-decoration-none small">
                                                <i class="uil-record-audio me-1"></i>Available
                                            </a>
                                        @else
                                            <span class="badge bg-light text-muted small">
                                                <i class="uil-times me-1"></i>N/A
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        <a href="{{ route('user.meetings.show', $meeting->id) }}"
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="uil-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="uil-history display-4 text-muted mb-3"></i>
                                        <h6 class="text-muted">No meeting history</h6>
                                        <p class="text-muted small">Your past meetings will appear here</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function refreshMeetings() {
        window.location.reload();
    }

    // Auto-refresh active meetings every 30 seconds
    @if($activeMeetings->count() > 0)
    setInterval(function() {
        // Only refresh if there are active meetings
        const activeMeetingsCount = {{ $activeMeetings->count() }};
        if (activeMeetingsCount > 0) {
            window.location.reload();
        }
    }, 30000);
    @endif
</script>
@endsection
